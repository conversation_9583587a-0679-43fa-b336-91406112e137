import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { FaSearch, FaFilter, FaSort } from 'react-icons/fa';
import config from '../../config';

export default function BrandCampaigns() {
  const navigate = useNavigate();
  const [campaigns, setCampaigns] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filterStatus, setFilterStatus] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('createdAt');
  const [currentPage, setCurrentPage] = useState(1);
  const campaignsPerPage = 6;

  // Mock data - replace with actual API call
  const mockCampaigns = [
    {
      id: 1,
      title: 'Glow Serum Ampoule',
      status: 'active',
      createdAt: '2025-05-18',
      deadline: '2025-06-03',
      creators: 6,
      submissionRate: 78,
      productName: 'Glow Serum'
    },
    {
      id: 2,
      title: 'Vita Bright Toner Pack',
      status: 'draft',
      createdAt: '2025-05-10',
      deadline: null,
      creators: 0,
      submissionRate: null,
      productName: 'Vita Bright Toner'
    },
    {
      id: 3,
      title: 'Collagen Sheet Mask',
      status: 'completed',
      createdAt: '2025-04-20',
      deadline: '2025-05-15',
      endedAt: '2025-05-15',
      creators: 10,
      submissionRate: 93,
      productName: 'Collagen Sheet Mask'
    },
    {
      id: 4,
      title: 'Vitamin C Essence',
      status: 'active',
      createdAt: '2025-05-15',
      deadline: '2025-06-10',
      creators: 8,
      submissionRate: 65,
      productName: 'Vitamin C Essence'
    },
    {
      id: 5,
      title: 'Hyaluronic Acid Serum',
      status: 'draft',
      createdAt: '2025-05-12',
      deadline: null,
      creators: 0,
      submissionRate: null,
      productName: 'Hyaluronic Acid Serum'
    },
    {
      id: 6,
      title: 'Retinol Night Cream',
      status: 'completed',
      createdAt: '2025-04-15',
      deadline: '2025-05-10',
      endedAt: '2025-05-10',
      creators: 12,
      submissionRate: 87,
      productName: 'Retinol Night Cream'
    }
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setCampaigns(mockCampaigns);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter and search logic
  const filteredCampaigns = campaigns
    .filter(campaign => {
      if (filterStatus === 'All') return true;
      return campaign.status.toLowerCase() === filterStatus.toLowerCase();
    })
    .filter(campaign => {
      if (!searchTerm) return true;
      return campaign.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
             campaign.productName.toLowerCase().includes(searchTerm.toLowerCase());
    })
    .sort((a, b) => {
      if (sortBy === 'deadline') {
        if (!a.deadline) return 1;
        if (!b.deadline) return -1;
        return new Date(a.deadline) - new Date(b.deadline);
      }
      if (sortBy === 'submissionRate') {
        return (b.submissionRate || 0) - (a.submissionRate || 0);
      }
      return new Date(b.createdAt) - new Date(a.createdAt);
    });

  // Pagination
  const indexOfLast = currentPage * campaignsPerPage;
  const indexOfFirst = indexOfLast - campaignsPerPage;
  const currentCampaigns = filteredCampaigns.slice(indexOfFirst, indexOfLast);
  const totalPages = Math.ceil(filteredCampaigns.length / campaignsPerPage);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active': return '🟢';
      case 'draft': return '📝';
      case 'completed': return '✅';
      default: return '⚪';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-green-400';
      case 'draft': return 'text-yellow-400';
      case 'completed': return 'text-blue-400';
      default: return 'text-gray-400';
    }
  };

  const handleCampaignClick = (campaignId) => {
    navigate(`/brand/campaigns/${campaignId}`);
  };

  return (
    <div className="min-h-screen bg-[#121212] text-gray-100 p-6">
      <Helmet>
        <title>My Campaigns | Matchably</title>
        <meta name="robots" content="noindex, nofollow" />
      </Helmet>

      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-[#E0FFFA] mb-2 FontNoto">
            My Campaigns
          </h1>
          <p className="text-gray-400 FontNoto">
            Manage all your registered campaigns
          </p>
        </div>

        {/* Filters and Search */}
        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            {/* Status Filters */}
            <div className="flex flex-wrap gap-2">
              {['All', 'Active', 'Draft', 'Completed'].map(status => (
                <button
                  key={status}
                  onClick={() => setFilterStatus(status)}
                  className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 FontNoto ${
                    filterStatus === status
                      ? 'bg-lime-500 text-black'
                      : 'bg-white/10 text-gray-300 hover:bg-white/20'
                  }`}
                >
                  {status}
                </button>
              ))}
            </div>

            {/* Search and Sort */}
            <div className="flex flex-col sm:flex-row gap-4 w-full lg:w-auto">
              {/* Search */}
              <div className="relative">
                <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search campaigns or products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-lime-500 FontNoto w-full sm:w-64"
                />
              </div>

              {/* Sort */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-lime-500 FontNoto"
              >
                <option value="createdAt">Sort by Creation Date</option>
                <option value="deadline">Sort by Deadline</option>
                <option value="submissionRate">Sort by Submission Rate</option>
              </select>
            </div>
          </div>
        </div>

        {/* Campaign Cards */}
        {loading ? (
          <div className="flex justify-center items-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-lime-500"></div>
          </div>
        ) : currentCampaigns.length === 0 ? (
          <div className="text-center py-20">
            <p className="text-xl text-gray-400 FontNoto mb-4">
              {filterStatus === 'All' ? 'No campaigns found' : `No ${filterStatus.toLowerCase()} campaigns found`}
            </p>
            {searchTerm && (
              <p className="text-gray-500 FontNoto">
                Try adjusting your search terms or filters
              </p>
            )}
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {currentCampaigns.map(campaign => (
                <div
                  key={campaign.id}
                  onClick={() => handleCampaignClick(campaign.id)}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 hover:border-lime-400/30 transition-all duration-200 cursor-pointer group"
                >
                  {/* Status and Title */}
                  <div className="mb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-lg">{getStatusIcon(campaign.status)}</span>
                      <span className={`font-semibold capitalize FontNoto ${getStatusColor(campaign.status)}`}>
                        [{campaign.status}]
                      </span>
                    </div>
                    <h3 className="text-xl font-bold text-[#E0FFFA] group-hover:text-lime-400 transition-colors duration-200 FontNoto">
                      {campaign.title}
                    </h3>
                  </div>

                  {/* Dates */}
                  <div className="mb-4 space-y-1">
                    <div className="flex items-center gap-2 text-sm">
                      <span className="text-gray-400 FontNoto">📆 Created:</span>
                      <span className="text-gray-300 FontNoto">{campaign.createdAt}</span>
                      {campaign.deadline && (
                        <>
                          <span className="text-gray-400 FontNoto">| Deadline:</span>
                          <span className="text-gray-300 FontNoto">{campaign.deadline}</span>
                        </>
                      )}
                    </div>
                    {campaign.endedAt && (
                      <div className="flex items-center gap-2 text-sm">
                        <span className="text-gray-400 FontNoto">🏁 Ended:</span>
                        <span className="text-gray-300 FontNoto">{campaign.endedAt}</span>
                      </div>
                    )}
                  </div>

                  {/* Stats */}
                  <div className="mb-4">
                    <div className="flex items-center gap-2 text-sm mb-2">
                      <span className="text-gray-400 FontNoto">👥</span>
                      <span className="text-lime-400 font-semibold FontNoto">{campaign.creators} Creators</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <span className="text-gray-400 FontNoto">📈 Submission Rate:</span>
                      <span className="text-lime-400 font-semibold FontNoto">
                        {campaign.submissionRate ? `${campaign.submissionRate}%` : '—'}
                      </span>
                    </div>
                  </div>

                  {/* View Details Button */}
                  <button className="w-full bg-lime-500/20 hover:bg-lime-500/30 text-lime-400 font-medium py-2 rounded-lg transition-all duration-200 FontNoto border border-lime-500/30 hover:border-lime-500/50">
                    View Details
                  </button>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center gap-2">
                {Array.from({ length: totalPages }).map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentPage(index + 1)}
                    className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 FontNoto ${
                      currentPage === index + 1
                        ? 'bg-lime-500 text-black'
                        : 'bg-white/10 text-gray-300 hover:bg-white/20'
                    }`}
                  >
                    {index + 1}
                  </button>
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
