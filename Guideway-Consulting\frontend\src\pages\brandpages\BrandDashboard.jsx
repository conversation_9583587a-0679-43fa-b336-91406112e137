import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import config from "../../config";
import { IoMdArrowRoundBack } from "react-icons/io";
import { FaMoneyBillWave, FaClock, FaGem } from "react-icons/fa"; // add these at the top
// Import UI Components
import CampaignStatsCard from "./dashboard/CampaignStatsCard";
import CampaignsEndingSoon from "./dashboard/CampaignsEndingSoon";
import BrandToDoCard from "./dashboard/BrandToDoCard";
import ExtensionRequestsCard from "./dashboard/ExtensionRequestsCard";

export default function BrandDashboard() {
  const [hasPlan, setHasPlan] = useState(true);
  const [subscription, setSubscription] = useState({
    plan: {
      name: "Professional Plan",
      price: 299,
      billingType: "monthly",
      validityMonths: 1
    }
  });
  const [loading, setLoading] = useState(false);
  const [campaigns, setCampaigns] = useState([]);
  const [campaignStats, setCampaignStats] = useState({
    total: 0,
    active: 0,
    draft: 0,
    completed: 0,
    averageSubmissionRate: 0
  });
  const [endingSoonCampaigns, setEndingSoonCampaigns] = useState([]);
  const [todoItems, setTodoItems] = useState([]);
  const navigate = useNavigate();
  const token = localStorage.getItem("BRAND_TOKEN");

  // Mock campaign data
  const mockCampaigns = [
    {
      id: 1,
      title: 'Glow Serum Ampoule',
      status: 'active',
      createdAt: '2025-05-18',
      deadline: '2025-06-03',
      creators: 6,
      submissionRate: 78,
      pendingApprovals: 2
    },
    {
      id: 2,
      title: 'Vita Bright Toner Pack',
      status: 'draft',
      createdAt: '2025-05-10',
      deadline: '2025-06-15',
      creators: 0,
      submissionRate: 0,
      pendingApprovals: 0
    },
    {
      id: 3,
      title: 'Collagen Sheet Mask',
      status: 'completed',
      createdAt: '2025-04-20',
      deadline: '2025-05-15',
      endedAt: '2025-05-15',
      creators: 10,
      submissionRate: 93,
      pendingApprovals: 0
    },
    {
      id: 4,
      title: 'Vitamin C Essence',
      status: 'active',
      createdAt: '2025-05-15',
      deadline: '2025-06-05',
      creators: 8,
      submissionRate: 65,
      pendingApprovals: 1
    }
  ];

  useEffect(() => {
    if (!token) {
      navigate('/brand-auth');
      return;
    }

    // Initialize dashboard data
    setCampaigns(mockCampaigns);

    // Calculate campaign statistics
    const stats = {
      total: mockCampaigns.length,
      active: mockCampaigns.filter(c => c.status === 'active').length,
      draft: mockCampaigns.filter(c => c.status === 'draft').length,
      completed: mockCampaigns.filter(c => c.status === 'completed').length,
      averageSubmissionRate: Math.round(
        mockCampaigns.reduce((sum, c) => sum + c.submissionRate, 0) / mockCampaigns.length
      )
    };
    setCampaignStats(stats);

    // Calculate campaigns ending soon (within 5 days)
    const today = new Date();
    const endingSoon = mockCampaigns
      .filter(c => c.status === 'active' && c.deadline)
      .map(c => {
        const deadlineDate = new Date(c.deadline);
        const daysLeft = Math.ceil((deadlineDate - today) / (1000 * 60 * 60 * 24));
        return { ...c, daysLeft };
      })
      .filter(c => c.daysLeft <= 5 && c.daysLeft >= 0)
      .sort((a, b) => a.daysLeft - b.daysLeft);

    setEndingSoonCampaigns(endingSoon);

    // Generate todo items
    const todos = [];
    const totalPendingApprovals = mockCampaigns.reduce((sum, c) => sum + c.pendingApprovals, 0);

    if (totalPendingApprovals > 0) {
      todos.push({
        id: 1,
        text: `Approve ${totalPendingApprovals} pending content${totalPendingApprovals > 1 ? 's' : ''}`,
        action: 'Go',
        color: 'yellow',
        icon: '🟡',
        onClick: () => navigate('/brand/campaigns')
      });
    }

    if (endingSoon.length > 0) {
      todos.push({
        id: 2,
        text: `${endingSoon.length} campaign${endingSoon.length > 1 ? 's' : ''} ending soon`,
        action: 'Review',
        color: 'red',
        icon: '🔴',
        onClick: () => navigate('/brand/campaigns')
      });
    }

    const draftCampaigns = mockCampaigns.filter(c => c.status === 'draft').length;
    if (draftCampaigns > 0) {
      todos.push({
        id: 3,
        text: `Complete ${draftCampaigns} draft campaign${draftCampaigns > 1 ? 's' : ''}`,
        action: 'Edit',
        color: 'orange',
        icon: '🟠',
        onClick: () => navigate('/brand/campaigns')
      });
    }

    setTodoItems(todos.slice(0, 5)); // Max 5 items

    console.log('✅ Brand dashboard loaded successfully');
    setLoading(false);
  }, [token, navigate]);


  return (
    <div className="min-h-screen bg-[#0d0d0d] text-white px-4 py-10 font-sans">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header with Create Campaign CTA */}
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl md:text-4xl font-extrabold tracking-tight text-[#E0FFFA]">
            Brand Dashboard
          </h1>
          <div className="flex items-center gap-4">
            {/* Create Campaign CTA Button */}
            {campaignStats.total === 0 ? (
              <button
                className="bg-lime-500 hover:bg-lime-600 text-black font-semibold py-3 px-6 rounded-xl transition duration-300 shadow-md flex items-center gap-2"
                onClick={() => navigate("/brand/create-campaign")}
              >
                + Create Your First Campaign
              </button>
            ) : (
              <button
                className="bg-lime-500 hover:bg-lime-600 text-black font-semibold py-2.5 px-5 rounded-xl transition duration-300 shadow-md flex items-center gap-2"
                onClick={() => navigate("/brand/create-campaign")}
              >
                + New Campaign
              </button>
            )}
            <button
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2.5 px-5 rounded-xl transition duration-300 shadow-md flex items-center gap-2"
              onClick={() => navigate("/forbrand")}
            >
              <IoMdArrowRoundBack size={20} /> Return to Home
            </button>
          </div>
        </div>

        {loading ? (
          <div className="text-center text-gray-400 text-lg">Loading your plan...</div>
        ) : !hasPlan ? (
          <div className="bg-[#1a1a1a] border border-[#2c2c2c] p-10 rounded-2xl shadow-lg text-center">
            <p className="mb-6 text-xl text-gray-300 font-medium">
              You don’t have an active plan.
            </p>
            <button
              onClick={() => navigate("/brand/pricing")}
              className="bg-gradient-to-r from-lime-400 to-lime-500 hover:from-lime-500 hover:to-lime-600 text-black font-semibold py-2.5 px-6 rounded-xl transition duration-300 shadow-md"
            >
              See Our Pricing
            </button>
          </div>
        ) : !hasCampaigns ? (
          <div className="bg-[#1a1a1a] border border-[#2c2c2c] p-10 rounded-2xl shadow-lg text-center">
            <p className="mb-6 text-xl text-gray-300 font-medium">
              You don’t have any campaigns yet.
            </p>
            <button
              onClick={() => navigate("/brand/create-campaign")}
              className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2.5 px-6 rounded-xl transition duration-300 shadow-md"
            >
              Create Your First Campaign
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Campaign Statistics Card */}
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
                <h2 className="text-xl font-bold text-[#E0FFFA] mb-4 FontNoto">Campaign Overview</h2>

                {/* Total Campaigns */}
                <div className="mb-6">
                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-2xl">📦</span>
                    <span className="text-lg font-semibold text-white FontNoto">
                      Total Campaigns: {campaignStats.total}
                    </span>
                  </div>

                  {/* Status Breakdown */}
                  <div className="flex flex-wrap gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <span className="text-green-400">🟢</span>
                      <span className="text-gray-300 FontNoto">Active: {campaignStats.active}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-yellow-400">📝</span>
                      <span className="text-gray-300 FontNoto">Draft: {campaignStats.draft}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-blue-400">✅</span>
                      <span className="text-gray-300 FontNoto">Completed: {campaignStats.completed}</span>
                    </div>
                  </div>
                </div>

                {/* Average Submission Rate */}
                <div className="mb-6">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-xl">📈</span>
                    <span className="text-lg font-semibold text-white FontNoto">
                      Average Submission Rate: {campaignStats.averageSubmissionRate}%
                    </span>
                    <button
                      onClick={() => navigate('/brand/campaigns')}
                      className="text-lime-400 hover:text-lime-300 text-sm underline FontNoto ml-2"
                    >
                      → View by campaign
                    </button>
                  </div>
                </div>

                {/* Campaigns Ending Soon */}
                {endingSoonCampaigns.length > 0 && (
                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <span className="text-xl">⏰</span>
                      <span className="text-lg font-semibold text-white FontNoto">Campaigns Ending Soon:</span>
                    </div>
                    <div className="space-y-2">
                      {endingSoonCampaigns.map(campaign => (
                        <div
                          key={campaign.id}
                          className="flex items-center justify-between bg-white/5 rounded-lg p-3 hover:bg-white/10 transition-colors cursor-pointer"
                          onClick={() => navigate(`/brand/campaigns/${campaign.id}`)}
                        >
                          <span className="text-gray-300 FontNoto">• {campaign.title}</span>
                          <span className={`font-semibold FontNoto ${
                            campaign.daysLeft <= 1 ? 'text-red-400' :
                            campaign.daysLeft <= 3 ? 'text-yellow-400' : 'text-orange-400'
                          }`}>
                            (D-{campaign.daysLeft})
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Plan Information */}
              {subscription.plan && (
                <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
                  <h2 className="text-lg font-semibold text-[#E0FFFA] mb-4 flex items-center gap-2 FontNoto">
                    <FaGem className="text-lime-400" /> Plan Information
                  </h2>
                  <div className="flex flex-wrap items-center gap-6 text-gray-300">
                    <div className="flex items-center gap-2">
                      <FaGem className="text-indigo-400" />
                      <span className="font-medium FontNoto">{subscription.plan.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <FaMoneyBillWave className="text-green-400" />
                      <span className="text-white font-bold text-lg FontNoto">${subscription.plan.price}</span>
                    </div>
                    <div className="flex items-center gap-2 border-l border-gray-600 pl-4">
                      <FaClock className="text-yellow-400" />
                      <span className="text-sm FontNoto">
                        {subscription.plan.billingType === "onetime" ? "One-time" : "Recurring"},{" "}
                        {subscription.plan.validityMonths}-month validity
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Right Column - Brand To-Do Widget */}
            <div className="space-y-6">
              {/* Brand To-Do Card */}
              {todoItems.length > 0 && (
                <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
                  <h2 className="text-lg font-semibold text-[#E0FFFA] mb-4 flex items-center gap-2 FontNoto">
                    📋 Brand To-Do
                  </h2>
                  <div className="space-y-3">
                    {todoItems.map(item => (
                      <div key={item.id} className="flex items-center justify-between p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                        <div className="flex items-center gap-3">
                          <span className="text-lg">{item.icon}</span>
                          <span className="text-gray-300 text-sm FontNoto">{item.text}</span>
                        </div>
                        <button
                          onClick={item.onClick}
                          className="bg-lime-500/20 hover:bg-lime-500/30 text-lime-400 px-3 py-1 rounded text-xs FontNoto transition-colors"
                        >
                          {item.action}
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Quick Actions */}
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
                <h2 className="text-lg font-semibold text-[#E0FFFA] mb-4 FontNoto">Quick Actions</h2>
                <div className="space-y-3">
                  <button
                    onClick={() => navigate('/brand/campaigns')}
                    className="w-full bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 py-2 rounded-lg transition-colors FontNoto"
                  >
                    View All Campaigns
                  </button>
                  <button
                    onClick={() => navigate('/brand/create-campaign')}
                    className="w-full bg-lime-500/20 hover:bg-lime-500/30 text-lime-400 py-2 rounded-lg transition-colors FontNoto"
                  >
                    Create New Campaign
                  </button>
                  <button
                    onClick={() => navigate('/brand/brand-settings')}
                    className="w-full bg-gray-500/20 hover:bg-gray-500/30 text-gray-400 py-2 rounded-lg transition-colors FontNoto"
                  >
                    Account Settings
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
