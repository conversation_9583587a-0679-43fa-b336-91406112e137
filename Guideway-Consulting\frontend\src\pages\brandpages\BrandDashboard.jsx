import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import config from "../../config";
import { IoMdArrowRoundBack } from "react-icons/io";
import { FaMoneyBillWave, FaClock, FaGem } from "react-icons/fa"; 

import BrandToDoCardFixed from "../../components/brand/BrandToDoCardFixed";
import BrandToDoCard from "../../components/brand/BrandToDoCard";

export default function BrandDashboard() {
  const [hasPlan, setHasPlan] = useState(true);
  const [subscription, setSubscription] = useState({
    plan: {
      name: "Professional Plan",
      price: 299,
      billingType: "monthly",
      validityMonths: 1
    }
  });
  const [loading, setLoading] = useState(false);
  const [campaigns, setCampaigns] = useState([]);
  const [campaignStats, setCampaignStats] = useState({
    total: 0,
    active: 0,
    draft: 0,
    completed: 0,
    averageSubmissionRate: 0
  });
  const [endingSoonCampaigns, setEndingSoonCampaigns] = useState([]);
  const navigate = useNavigate();
  const token = localStorage.getItem("BRAND_TOKEN");

  // Mock campaign data with required fields for BrandToDoCard
  const mockCampaigns = [
    {
      id: 1,
      title: 'Glow Serum Ampoule',
      status: 'active',
      createdAt: '2025-05-18',
      deadline: '2025-06-21', // 2 days from now
      creators: 6,
      submissionRate: 78,
      pendingSubmissions: 3,
      pendingApplications: 2,
      approvedCreators: 4,
      hasTracking: false
    },
    {
      id: 2,
      title: 'Vita Bright Toner Pack',
      status: 'draft',
      createdAt: '2025-05-10',
      deadline: '2025-06-25',
      creators: 0,
      submissionRate: 0,
      pendingSubmissions: 0,
      pendingApplications: 0,
      approvedCreators: 0,
      hasTracking: false
    },
    {
      id: 3,
      title: 'Collagen Sheet Mask',
      status: 'completed',
      createdAt: '2025-04-20',
      deadline: '2025-05-15',
      endedAt: '2025-05-15',
      creators: 10,
      submissionRate: 93,
      pendingSubmissions: 0,
      pendingApplications: 0,
      approvedCreators: 10,
      hasTracking: true
    },
    {
      id: 4,
      title: 'Vitamin C Essence',
      status: 'active',
      createdAt: '2025-05-15',
      deadline: '2025-06-22', // 3 days from now
      creators: 8,
      submissionRate: 65,
      pendingSubmissions: 1,
      pendingApplications: 3,
      approvedCreators: 5,
      hasTracking: true
    }
  ];

  useEffect(() => {
    if (!token) {
      navigate('/brand-auth');
      return;
    }

    // Initialize dashboard data
    setCampaigns(mockCampaigns);

    // Calculate campaign statistics
    const stats = {
      total: mockCampaigns.length,
      active: mockCampaigns.filter(c => c.status === 'active').length,
      draft: mockCampaigns.filter(c => c.status === 'draft').length,
      completed: mockCampaigns.filter(c => c.status === 'completed').length,
      averageSubmissionRate: Math.round(
        mockCampaigns.reduce((sum, c) => sum + c.submissionRate, 0) / mockCampaigns.length
      )
    };
    setCampaignStats(stats);

    // Calculate campaigns ending soon (within 5 days)
    const today = new Date();
    const endingSoon = mockCampaigns
      .filter(c => c.status === 'active' && c.deadline)
      .map(c => {
        const deadlineDate = new Date(c.deadline);
        const daysLeft = Math.ceil((deadlineDate - today) / (1000 * 60 * 60 * 24));
        return { ...c, daysLeft };
      })
      .filter(c => c.daysLeft <= 5 && c.daysLeft >= 0)
      .sort((a, b) => a.daysLeft - b.daysLeft);

    setEndingSoonCampaigns(endingSoon);

    console.log('✅ Brand dashboard loaded successfully');
    setLoading(false);
  }, [token, navigate]);


  return (
    <div className="min-h-screen bg-[#0d0d0d] text-white px-4 py-10 font-sans">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header with Create Campaign CTA */}
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl md:text-4xl font-extrabold tracking-tight text-[#E0FFFA]">
            Brand Dashboard
          </h1>
          <div className="flex items-center gap-4">
            {/* Create Campaign CTA Button */}
            {campaignStats.total === 0 ? (
              <button
                className="bg-lime-500 hover:bg-lime-600 text-black font-semibold py-3 px-6 rounded-xl transition duration-300 shadow-md flex items-center gap-2"
                onClick={() => navigate("/brand/campaigns/new")}
              >
                + Create Your First Campaign
              </button>
            ) : (
              <button
                className="bg-lime-500 hover:bg-lime-600 text-black font-semibold py-2.5 px-5 rounded-xl transition duration-300 shadow-md flex items-center gap-2"
                onClick={() => navigate("/brand/campaigns/new")}
              >
                + New Campaign
              </button>
            )}
            <button
              className="bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2.5 px-5 rounded-xl transition duration-300 shadow-md flex items-center gap-2"
              onClick={() => navigate("/brand")}
            >
              <IoMdArrowRoundBack size={20} /> Back to Brand
            </button>
          </div>
        </div>

        {loading ? (
          <div className="text-center text-gray-400 text-lg">Loading your plan...</div>
        ) : !hasPlan ? (
          <div className="bg-[#1a1a1a] border border-[#2c2c2c] p-10 rounded-2xl shadow-lg text-center">
            <p className="mb-6 text-xl text-gray-300 font-medium">
              You don’t have an active plan.
            </p>
            <button
              onClick={() => navigate("/brand/pricing")}
              className="bg-gradient-to-r from-lime-400 to-lime-500 hover:from-lime-500 hover:to-lime-600 text-black font-semibold py-2.5 px-6 rounded-xl transition duration-300 shadow-md"
            >
              See Our Pricing
            </button>
          </div>
        ) : campaignStats.total === 0 ? (
          <div className="bg-[#1a1a1a] border border-[#2c2c2c] p-10 rounded-2xl shadow-lg text-center">
            <p className="mb-6 text-xl text-gray-300 font-medium">
              You don’t have any campaigns yet.
            </p>
            <button
              onClick={() => navigate("/brand/create-campaign")}
              className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2.5 px-6 rounded-xl transition duration-300 shadow-md"
            >
              Create Your First Campaign
            </button>
          </div>
        ) : (
          <div className="space-y-8">
            {/* Campaign Summary Cards */}
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
              <h2 className="text-xl font-bold text-[#E0FFFA] mb-6 FontNoto">📦 Campaign Summary</h2>

              {/* Total Campaigns with breakdown */}
              <div className="mb-6">
                <div className="text-lg font-semibold text-white FontNoto mb-3">
                  Total Campaigns: {campaignStats.total}
                </div>
                <div className="flex flex-wrap gap-6 text-sm">
                  <div className="flex items-center gap-2">
                    <span className="text-green-400">🟢</span>
                    <span className="text-gray-300 FontNoto">Active: {campaignStats.active}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-yellow-400">📝</span>
                    <span className="text-gray-300 FontNoto">Draft: {campaignStats.draft}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-blue-400">✅</span>
                    <span className="text-gray-300 FontNoto">Completed: {campaignStats.completed}</span>
                  </div>
                </div>
              </div>

              {/* Average Submission Rate - clickable */}
              <div className="mb-6">
                <div className="flex items-center gap-2">
                  <span className="text-lg font-semibold text-white FontNoto">
                    📈 Average Submission Rate: {campaignStats.averageSubmissionRate}%
                  </span>
                  <button
                    onClick={() => navigate('/brand/campaigns')}
                    className="text-lime-400 hover:text-lime-300 text-sm underline FontNoto transition-colors"
                  >
                    → View by campaign
                  </button>
                </div>
              </div>

              {/* Campaigns Ending Soon with D-Day style */}
              {endingSoonCampaigns.length > 0 && (
                <div>
                  <div className="text-lg font-semibold text-white FontNoto mb-3">
                    ⏰ Campaigns Ending Soon:
                  </div>
                  <div className="space-y-2">
                    {endingSoonCampaigns.map(campaign => (
                      <div
                        key={campaign.id}
                        className="flex items-center justify-between bg-white/5 rounded-lg p-3 hover:bg-white/10 transition-colors cursor-pointer"
                        onClick={() => navigate(`/brand/campaigns/${campaign.id}`)}
                      >
                        <span className="text-gray-300 FontNoto">• {campaign.title}</span>
                        <span className={`font-semibold FontNoto ${
                          campaign.daysLeft <= 1 ? 'text-red-400' :
                          campaign.daysLeft <= 3 ? 'text-yellow-400' : 'text-orange-400'
                        }`}>
                          (D-{campaign.daysLeft})
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Mobile BrandToDoCard - above Plan Info */}
            <div className="lg:hidden">
              <BrandToDoCard campaigns={campaigns} />
            </div>

            {/* Plan Information */}
            {subscription.plan && (
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
                <h2 className="text-lg font-semibold text-[#E0FFFA] mb-4 flex items-center gap-2 FontNoto">
                  <FaGem className="text-lime-400" /> Plan Information
                </h2>
                <div className="flex flex-wrap items-center gap-6 text-gray-300">
                  <div className="flex items-center gap-2">
                    <FaGem className="text-indigo-400" />
                    <span className="font-medium FontNoto">{subscription.plan.name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <FaMoneyBillWave className="text-green-400" />
                    <span className="text-white font-bold text-lg FontNoto">${subscription.plan.price}</span>
                  </div>
                  <div className="flex items-center gap-2 border-l border-gray-600 pl-4">
                    <FaClock className="text-yellow-400" />
                    <span className="text-sm FontNoto">
                      {subscription.plan.billingType === "onetime" ? "One-time" : "Recurring"},{" "}
                      {subscription.plan.validityMonths}-month validity
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Desktop Fixed BrandToDoCard - bottom-right */}
        <BrandToDoCardFixed campaigns={campaigns} />
      </div>
    </div>
  );
}
