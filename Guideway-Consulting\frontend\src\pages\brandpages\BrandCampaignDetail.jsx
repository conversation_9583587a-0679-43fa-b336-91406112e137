import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { FaArrowLeft, FaExternalLinkAlt, FaEdit, FaSave, FaTimes, FaCheck, FaBan, FaTruck } from 'react-icons/fa';
import config from '../../config';

export default function BrandCampaignDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [campaign, setCampaign] = useState(null);
  const [creators, setCreators] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editingTracking, setEditingTracking] = useState(null);
  const [trackingInput, setTrackingInput] = useState('');
  const [showTrackingModal, setShowTrackingModal] = useState(false);
  const [selectedCreator, setSelectedCreator] = useState(null);
  const [trackingForm, setTrackingForm] = useState({
    courier: 'UPS',
    trackingNumber: '',
    customCourier: ''
  });
  const [updating, setUpdating] = useState(false);

  // Mock campaign data
  const mockCampaign = {
    id: parseInt(id),
    title: 'Glow Serum Ampoule',
    status: 'active',
    createdAt: '2025-05-18',
    deadline: '2025-06-03',
    description: 'Promote our new vitamin C glow serum to skincare enthusiasts',
    budget: 5000,
    targetAudience: 'Skincare enthusiasts, ages 18-35',
    requirements: 'Must have 10K+ followers, skincare niche'
  };

  // Mock creators data
  const mockCreators = [
    {
      id: 1,
      name: 'Sarah Kim',
      socialId: '@sarahskincare',
      platform: 'Instagram',
      profileUrl: 'https://instagram.com/sarahskincare',
      performanceScore: 8.5,
      tracking: 'TW123456789KR',
      contentStatus: 'Approved',
      appliedAt: '2025-05-20',
      approvedAt: '2025-05-22'
    },
    {
      id: 2,
      name: 'Jessica Lee',
      socialId: '@jessbeauty',
      platform: 'TikTok',
      profileUrl: 'https://tiktok.com/@jessbeauty',
      performanceScore: 9.2,
      tracking: '',
      contentStatus: 'Applied',
      appliedAt: '2025-05-21',
      approvedAt: null
    },
    {
      id: 3,
      name: 'Emma Chen',
      socialId: '@emmaskintips',
      platform: 'Instagram',
      profileUrl: 'https://instagram.com/emmaskintips',
      performanceScore: 7.8,
      tracking: 'TW987654321KR',
      contentStatus: 'Not Submitted',
      appliedAt: '2025-05-19',
      approvedAt: '2025-05-21'
    },
    {
      id: 4,
      name: 'Mia Park',
      socialId: '@miabeautylife',
      platform: 'TikTok',
      profileUrl: 'https://tiktok.com/@miabeautylife',
      performanceScore: 8.9,
      tracking: 'TW456789123KR',
      contentStatus: 'Submitted',
      appliedAt: '2025-05-18',
      approvedAt: '2025-05-20'
    }
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setCampaign(mockCampaign);
      setCreators(mockCreators);
      setLoading(false);
    }, 1000);
  }, [id]);

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'applied': return 'text-yellow-400 bg-yellow-400/10';
      case 'approved': return 'text-green-400 bg-green-400/10';
      case 'not submitted': return 'text-red-400 bg-red-400/10';
      case 'submitted': return 'text-blue-400 bg-blue-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  const getPlatformIcon = (platform) => {
    return platform === 'Instagram' ? '📷' : '🎵';
  };

  const getPerformanceColor = (score) => {
    if (score >= 9) return 'text-green-400';
    if (score >= 7) return 'text-yellow-400';
    return 'text-red-400';
  };

  const handleSocialClick = (url) => {
    window.open(url, '_blank');
  };

  const handleTrackingEdit = (creatorId, currentTracking) => {
    setEditingTracking(creatorId);
    setTrackingInput(currentTracking);
  };

  const handleTrackingSave = (creatorId) => {
    setCreators(creators.map(creator => 
      creator.id === creatorId 
        ? { ...creator, tracking: trackingInput }
        : creator
    ));
    setEditingTracking(null);
    setTrackingInput('');
  };

  const handleTrackingCancel = () => {
    setEditingTracking(null);
    setTrackingInput('');
  };

  const handleTrackingClick = (tracking) => {
    if (tracking) {
      window.open(`https://www.17track.net/en/track?nums=${tracking}`, '_blank');
    }
  };

  const handleTrackingModalOpen = (creator) => {
    setSelectedCreator(creator);
    setTrackingForm({
      courier: creator.courier || 'UPS',
      trackingNumber: creator.tracking || '',
      customCourier: ''
    });
    setShowTrackingModal(true);
  };

  const handleTrackingModalClose = () => {
    setShowTrackingModal(false);
    setSelectedCreator(null);
    setTrackingForm({
      courier: 'UPS',
      trackingNumber: '',
      customCourier: ''
    });
  };

  const handleTrackingSubmit = async () => {
    if (!trackingForm.trackingNumber.trim()) {
      alert('Please enter a tracking number');
      return;
    }

    if (trackingForm.trackingNumber.length > 50) {
      alert('Tracking number must be less than 50 characters');
      return;
    }

    setUpdating(true);
    try {
      const token = localStorage.getItem('BRAND_TOKEN');
      const response = await fetch(`${config.BACKEND_URL}/api/brand/creators/${selectedCreator.id}/tracking`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          trackingNumber: trackingForm.trackingNumber,
          courier: trackingForm.courier === 'Other' ? trackingForm.customCourier : trackingForm.courier
        })
      });

      const data = await response.json();

      if (data.status === 'success') {
        // Update local state
        setCreators(creators.map(creator =>
          creator.id === selectedCreator.id
            ? { ...creator, tracking: trackingForm.trackingNumber, courier: trackingForm.courier }
            : creator
        ));
        handleTrackingModalClose();
        alert('Tracking information updated and email sent to creator!');
      } else {
        alert(data.message || 'Failed to update tracking information');
      }
    } catch (error) {
      console.error('Error updating tracking:', error);
      alert('Failed to update tracking information');
    } finally {
      setUpdating(false);
    }
  };

  const handleStatusUpdate = async (creatorId, newStatus) => {
    if (!confirm(`Are you sure you want to ${newStatus.toLowerCase()} this creator?`)) {
      return;
    }

    setUpdating(true);
    try {
      const token = localStorage.getItem('BRAND_TOKEN');
      const response = await fetch(`${config.BACKEND_URL}/api/brand/creators/${creatorId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ status: newStatus })
      });

      const data = await response.json();

      if (data.status === 'success') {
        // Update local state
        setCreators(creators.map(creator =>
          creator.id === creatorId
            ? { ...creator, contentStatus: newStatus }
            : creator
        ));
        alert(data.message);
      } else {
        alert(data.message || 'Failed to update creator status');
      }
    } catch (error) {
      console.error('Error updating status:', error);
      alert('Failed to update creator status');
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#121212] text-gray-100 p-6 flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-lime-500"></div>
      </div>
    );
  }

  if (!campaign) {
    return (
      <div className="min-h-screen bg-[#121212] text-gray-100 p-6">
        <div className="text-center py-20">
          <p className="text-xl text-gray-400 FontNoto">Campaign not found</p>
          <button
            onClick={() => navigate('/brand/campaigns')}
            className="mt-4 bg-lime-500 hover:bg-lime-600 text-black px-6 py-2 rounded-lg FontNoto font-medium"
          >
            Back to Campaigns
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#121212] text-gray-100 p-6">
      <Helmet>
        <title>{campaign.title} | Campaign Details</title>
        <meta name="robots" content="noindex, nofollow" />
      </Helmet>

      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/brand/campaigns')}
            className="flex items-center gap-2 text-gray-400 hover:text-lime-400 mb-4 FontNoto transition-colors duration-200"
          >
            <FaArrowLeft />
            Back to Campaigns
          </button>
          
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
            <h1 className="text-3xl font-bold text-[#E0FFFA] mb-4 FontNoto">
              {campaign.title}
            </h1>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-400 FontNoto">Status:</span>
                <span className="text-lime-400 font-semibold ml-2 FontNoto capitalize">
                  {campaign.status}
                </span>
              </div>
              <div>
                <span className="text-gray-400 FontNoto">Created:</span>
                <span className="text-gray-300 ml-2 FontNoto">{campaign.createdAt}</span>
              </div>
              <div>
                <span className="text-gray-400 FontNoto">Deadline:</span>
                <span className="text-gray-300 ml-2 FontNoto">{campaign.deadline}</span>
              </div>
              <div>
                <span className="text-gray-400 FontNoto">Budget:</span>
                <span className="text-gray-300 ml-2 FontNoto">${campaign.budget}</span>
              </div>
            </div>
            
            <div className="mt-4">
              <p className="text-gray-300 FontNoto">{campaign.description}</p>
            </div>
          </div>
        </div>

        {/* Creators Table */}
        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden">
          <div className="p-6 border-b border-white/10">
            <h2 className="text-xl font-bold text-[#E0FFFA] FontNoto">
              Creator Applications ({creators.length})
            </h2>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white/5">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Name & Social ID
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Platform
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Performance Score
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Tracking
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Content Status
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-white/10">
                {creators.map((creator) => (
                  <tr key={creator.id} className="hover:bg-white/5 transition-colors duration-200">
                    {/* Name & Social ID */}
                    <td className="px-6 py-4">
                      <div className="flex flex-col">
                        <button
                          onClick={() => handleSocialClick(creator.profileUrl)}
                          className="text-[#E0FFFA] hover:text-lime-400 font-medium FontNoto text-left flex items-center gap-2 transition-colors duration-200"
                        >
                          {creator.name}
                          <FaExternalLinkAlt className="text-xs" />
                        </button>
                        <button
                          onClick={() => handleSocialClick(creator.profileUrl)}
                          className="text-gray-400 hover:text-lime-400 text-sm FontNoto text-left transition-colors duration-200"
                        >
                          {creator.socialId}
                        </button>
                      </div>
                    </td>

                    {/* Platform */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{getPlatformIcon(creator.platform)}</span>
                        <span className="text-gray-300 FontNoto">{creator.platform}</span>
                      </div>
                    </td>

                    {/* Performance Score */}
                    <td className="px-6 py-4">
                      <span className={`font-semibold FontNoto ${getPerformanceColor(creator.performanceScore)}`}>
                        {creator.performanceScore}/10
                      </span>
                    </td>

                    {/* Tracking */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-2">
                        {creator.tracking ? (
                          <button
                            onClick={() => handleTrackingClick(creator.tracking)}
                            className="text-lime-400 hover:text-lime-300 FontNoto text-sm underline flex items-center gap-1"
                          >
                            {creator.tracking}
                            <FaExternalLinkAlt className="text-xs" />
                          </button>
                        ) : (
                          <span className="text-gray-500 FontNoto text-sm">No tracking</span>
                        )}
                        <button
                          onClick={() => handleTrackingModalOpen(creator)}
                          className="text-gray-400 hover:text-lime-400 p-1"
                          title="Edit tracking"
                        >
                          <FaEdit className="text-xs" />
                        </button>
                      </div>
                    </td>

                    {/* Content Status */}
                    <td className="px-6 py-4">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium FontNoto ${getStatusColor(creator.contentStatus)}`}>
                        {creator.contentStatus}
                      </span>
                    </td>

                    {/* Actions */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-2">
                        {creator.contentStatus === 'Applied' && (
                          <>
                            <button
                              onClick={() => handleStatusUpdate(creator.id, 'Approved')}
                              disabled={updating}
                              className="bg-green-500/20 hover:bg-green-500/30 text-green-400 px-3 py-1 rounded text-sm FontNoto flex items-center gap-1 transition-colors duration-200 disabled:opacity-50"
                              title="Approve creator"
                            >
                              <FaCheck className="text-xs" />
                              Approve
                            </button>
                            <button
                              onClick={() => handleStatusUpdate(creator.id, 'Rejected')}
                              disabled={updating}
                              className="bg-red-500/20 hover:bg-red-500/30 text-red-400 px-3 py-1 rounded text-sm FontNoto flex items-center gap-1 transition-colors duration-200 disabled:opacity-50"
                              title="Reject creator"
                            >
                              <FaBan className="text-xs" />
                              Reject
                            </button>
                          </>
                        )}
                        {creator.contentStatus === 'Approved' && (
                          <button
                            onClick={() => handleTrackingModalOpen(creator)}
                            className="bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 px-3 py-1 rounded text-sm FontNoto flex items-center gap-1 transition-colors duration-200"
                            title="Add tracking"
                          >
                            <FaTruck className="text-xs" />
                            Add Tracking
                          </button>
                        )}
                        {(creator.contentStatus === 'Rejected' || creator.contentStatus === 'Not Submitted' || creator.contentStatus === 'Submitted') && (
                          <span className="text-gray-500 text-sm FontNoto">No actions</span>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {creators.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-400 FontNoto">No creator applications yet</p>
            </div>
          )}
        </div>

        {/* Tracking Modal */}
        {showTrackingModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-[#1f1f1f] rounded-2xl p-6 w-full max-w-md border border-white/10">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold text-[#E0FFFA] FontNoto">
                  Update Tracking - {selectedCreator?.name}
                </h3>
                <button
                  onClick={handleTrackingModalClose}
                  className="text-gray-400 hover:text-white"
                >
                  <FaTimes />
                </button>
              </div>

              <div className="space-y-4">
                {/* Courier Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2 FontNoto">
                    Courier
                  </label>
                  <select
                    value={trackingForm.courier}
                    onChange={(e) => setTrackingForm({...trackingForm, courier: e.target.value})}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white FontNoto focus:outline-none focus:ring-2 focus:ring-lime-500"
                  >
                    <option value="UPS">UPS</option>
                    <option value="FedEx">FedEx</option>
                    <option value="USPS">USPS</option>
                    <option value="Other">Other</option>
                  </select>
                </div>

                {/* Custom Courier Input */}
                {trackingForm.courier === 'Other' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2 FontNoto">
                      Custom Courier Name
                    </label>
                    <input
                      type="text"
                      value={trackingForm.customCourier}
                      onChange={(e) => setTrackingForm({...trackingForm, customCourier: e.target.value})}
                      placeholder="Enter courier name"
                      className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 FontNoto focus:outline-none focus:ring-2 focus:ring-lime-500"
                    />
                  </div>
                )}

                {/* Tracking Number */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2 FontNoto">
                    Tracking Number (Max 50 characters)
                  </label>
                  <input
                    type="text"
                    value={trackingForm.trackingNumber}
                    onChange={(e) => setTrackingForm({...trackingForm, trackingNumber: e.target.value})}
                    placeholder="Enter tracking number"
                    maxLength={50}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 FontNoto focus:outline-none focus:ring-2 focus:ring-lime-500"
                  />
                  <p className="text-xs text-gray-400 mt-1 FontNoto">
                    Creator will receive: https://www.17track.net/en/track?nums={trackingForm.trackingNumber}
                  </p>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 pt-4">
                  <button
                    onClick={handleTrackingSubmit}
                    disabled={updating || !trackingForm.trackingNumber.trim()}
                    className="flex-1 bg-lime-500 hover:bg-lime-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg FontNoto font-medium transition-colors duration-200"
                  >
                    {updating ? 'Saving...' : 'Save & Send Email'}
                  </button>
                  <button
                    onClick={handleTrackingModalClose}
                    disabled={updating}
                    className="px-4 py-2 bg-white/10 hover:bg-white/20 text-gray-300 rounded-lg FontNoto transition-colors duration-200"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
