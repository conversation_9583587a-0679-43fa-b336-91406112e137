import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { FaArrowLeft, FaExternalLinkAlt, FaEdit, FaSave, FaTimes, FaCheck, FaBan, FaTruck } from 'react-icons/fa';
import config from '../../config';

export default function BrandCampaignDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [campaign, setCampaign] = useState(null);
  const [creators, setCreators] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editingTracking, setEditingTracking] = useState(null);
  const [trackingInput, setTrackingInput] = useState('');
  const [showTrackingModal, setShowTrackingModal] = useState(false);
  const [selectedCreator, setSelectedCreator] = useState(null);
  const [trackingForm, setTrackingForm] = useState({
    courier: 'UPS',
    trackingNumber: '',
    customCourier: ''
  });
  const [updating, setUpdating] = useState(false);
  const [performanceScores, setPerformanceScores] = useState({});
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [showContentModal, setShowContentModal] = useState(false);
  const [selectedContent, setSelectedContent] = useState(null);
  const [contentStatus, setContentStatus] = useState('Pending');

  // Campaign and creators will be fetched from API

  useEffect(() => {
    fetchCampaignData();
  }, [id]);

  const fetchCampaignData = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('BRAND_TOKEN');

      // Fetch campaign details
      const campaignResponse = await fetch(`${config.BACKEND_URL}/api/brand/campaigns/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const campaignData = await campaignResponse.json();

      if (campaignData.status === 'success') {
        setCampaign(campaignData.campaign);

        // Fetch creators for this campaign
        const creatorsResponse = await fetch(`${config.BACKEND_URL}/api/brand/creators/${id}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        const creatorsData = await creatorsResponse.json();

        if (creatorsData.status === 'success') {
          // Transform creator data to match frontend expectations
          const transformedCreators = creatorsData.data.map(creator => ({
            id: creator.id,
            name: creator.name,
            email: creator.email,
            socialId: creator.socialId,
            platform: creator.platform,
            profileUrl: creator.profileUrl,
            performanceScore: creator.performanceScore,
            tracking: creator.tracking || '',
            deliveryStatus: creator.deliveryStatus || 'Not Found',
            deliveredAt: creator.deliveredAt,
            contentStatus: creator.contentStatus,
            extensionStatus: creator.extensionStatus,
            participationStatus: creator.contentStatus === 'Applied' ? 'Applied' :
                                creator.approvedAt ? 'Approved' : 'Applied',
            appliedAt: creator.appliedAt,
            approvedAt: creator.approvedAt,
            shippingInfo: creator.shippingInfo,
            hasContent: creator.contentStatus === 'Submitted' || creator.contentStatus === 'Approved',
            contentUrls: creator.contentUrls || { instagram: null, tiktok: null }
          }));

          setCreators(transformedCreators);

          // Fetch performance scores for all creators
          fetchPerformanceScores(transformedCreators);
        } else {
          setCreators([]);
        }
      } else {
        setCampaign(null);
        setCreators([]);
      }
    } catch (error) {
      console.error('Error fetching campaign data:', error);
      setCampaign(null);
      setCreators([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchPerformanceScores = async (creators) => {
    const scores = {};
    for (const creator of creators) {
      try {
        const token = localStorage.getItem('BRAND_TOKEN');
        const response = await fetch(`${config.BACKEND_URL}/api/brand/performance/${creator.id}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        const data = await response.json();
        if (data.status === 'success') {
          scores[creator.id] = data.data;
        }
      } catch (error) {
        console.error(`Error fetching performance for creator ${creator.id}:`, error);
        // Fallback to mock data
        scores[creator.id] = {
          score: creator.performanceScore,
          display: creator.performanceScore ? creator.performanceScore.toFixed(1) : '—',
          colorIndicator: getPerformanceColorIndicator(creator.performanceScore),
          hasMinimumCampaigns: creator.performanceScore !== null,
          tooltip: creator.performanceScore
            ? `Performance based on last 10 campaigns with the brand`
            : 'Performance Score is calculated after participating in 2 or more campaigns.'
        };
      }
    }
    setPerformanceScores(scores);
  };

  const getPerformanceColorIndicator = (score) => {
    if (score === null || score === undefined) return '⚪'; // Gray for N/A
    if (score >= 4.5) return '🟢'; // Green (4.5-5.0)
    if (score >= 3.5) return '🟠'; // Orange (3.5-4.4)
    return '🔴'; // Red (0.0-3.4)
  };

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'applied': return 'text-yellow-400 bg-yellow-400/10';
      case 'approved': return 'text-green-400 bg-green-400/10';
      case 'not submitted': return 'text-red-400 bg-red-400/10';
      case 'submitted': return 'text-blue-400 bg-blue-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  const getPlatformIcon = (platform) => {
    return platform === 'Instagram' ? '📷' : '🎵';
  };

  const getDeliveryStatusIcon = (status) => {
    switch (status) {
      case 'Not Found': return '❌';
      case 'Pending': return '⏳';
      case 'In Transit': return '🚚';
      case 'Delivered': return '📦';
      case 'Exception': return '⚠️';
      default: return '❓';
    }
  };

  const getDeliveryStatusColor = (status) => {
    switch (status) {
      case 'Not Found': return 'text-red-400';
      case 'Pending': return 'text-yellow-400';
      case 'In Transit': return 'text-blue-400';
      case 'Delivered': return 'text-green-400';
      case 'Exception': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getExtensionStatusColor = (status) => {
    if (!status) return 'text-gray-400';
    if (status.includes('Pending')) return 'text-yellow-400';
    if (status.includes('Approved')) return 'text-green-400';
    if (status.includes('Rejected')) return 'text-red-400';
    return 'text-gray-400';
  };

  const getParticipationStatusColor = (status) => {
    switch (status) {
      case 'Applied': return 'text-yellow-400 bg-yellow-400/10';
      case 'Approved': return 'text-green-400 bg-green-400/10';
      case 'Rejected': return 'text-red-400 bg-red-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  const handleSocialClick = (url) => {
    window.open(url, '_blank');
  };

  const handleTrackingEdit = (creatorId, currentTracking) => {
    setEditingTracking(creatorId);
    setTrackingInput(currentTracking);
  };

  const handleTrackingSave = (creatorId) => {
    setCreators(creators.map(creator => 
      creator.id === creatorId 
        ? { ...creator, tracking: trackingInput }
        : creator
    ));
    setEditingTracking(null);
    setTrackingInput('');
  };

  const handleTrackingCancel = () => {
    setEditingTracking(null);
    setTrackingInput('');
  };

  const handleTrackingClick = (tracking) => {
    if (tracking) {
      window.open(`https://www.17track.net/en/track?nums=${tracking}`, '_blank');
    }
  };

  const handleTrackingModalOpen = (creator) => {
    setSelectedCreator(creator);
    setTrackingForm({
      courier: creator.courier || 'UPS',
      trackingNumber: creator.tracking || '',
      customCourier: ''
    });
    setShowTrackingModal(true);
  };

  const handleTrackingModalClose = () => {
    setShowTrackingModal(false);
    setSelectedCreator(null);
    setTrackingForm({
      courier: 'UPS',
      trackingNumber: '',
      customCourier: ''
    });
  };

  const handleTrackingSubmit = async () => {
    if (!trackingForm.trackingNumber.trim()) {
      alert('Please enter a tracking number');
      return;
    }

    if (trackingForm.trackingNumber.length > 50) {
      alert('Tracking number must be less than 50 characters');
      return;
    }

    setUpdating(true);
    try {
      const token = localStorage.getItem('BRAND_TOKEN');
      const response = await fetch(`${config.BACKEND_URL}/api/brand/creators/${selectedCreator.id}/tracking`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          trackingNumber: trackingForm.trackingNumber,
          courier: trackingForm.courier === 'Other' ? trackingForm.customCourier : trackingForm.courier
        })
      });

      const data = await response.json();

      if (data.status === 'success') {
        // Update local state
        setCreators(creators.map(creator =>
          creator.id === selectedCreator.id
            ? { ...creator, tracking: trackingForm.trackingNumber, courier: trackingForm.courier }
            : creator
        ));

        // Update performance score for tracking input
        try {
          await fetch(`${config.BACKEND_URL}/api/brand/performance/tracking`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              creatorId: selectedCreator.creatorId || selectedCreator.id,
              campaignId: campaign.id,
              trackingDate: new Date().toISOString()
            })
          });

          // Refresh performance scores
          fetchPerformanceScores(creators);
        } catch (performanceError) {
          console.error('Error updating performance score:', performanceError);
        }

        handleTrackingModalClose();
        alert('Tracking information updated and email sent to creator!');
      } else {
        alert(data.message || 'Failed to update tracking information');
      }
    } catch (error) {
      console.error('Error updating tracking:', error);
      alert('Failed to update tracking information');
    } finally {
      setUpdating(false);
    }
  };

  const handleStatusUpdate = async (creatorId, newStatus) => {
    if (!confirm(`Are you sure you want to ${newStatus.toLowerCase()} this creator?`)) {
      return;
    }

    setUpdating(true);
    try {
      const token = localStorage.getItem('BRAND_TOKEN');
      const response = await fetch(`${config.BACKEND_URL}/api/brand/creators/${creatorId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ status: newStatus })
      });

      const data = await response.json();

      if (data.status === 'success') {
        // Update local state
        setCreators(creators.map(creator =>
          creator.id === creatorId
            ? { ...creator, contentStatus: newStatus }
            : creator
        ));

        // Update performance score for status change
        try {
          await fetch(`${config.BACKEND_URL}/api/brand/performance/status`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              creatorId: creators.find(c => c.id === creatorId)?.creatorId || creatorId,
              campaignId: campaign.id,
              status: newStatus
            })
          });

          // Refresh performance scores
          fetchPerformanceScores(creators);
        } catch (performanceError) {
          console.error('Error updating performance score:', performanceError);
        }

        alert(data.message);
      } else {
        alert(data.message || 'Failed to update creator status');
      }
    } catch (error) {
      console.error('Error updating status:', error);
      alert('Failed to update creator status');
    } finally {
      setUpdating(false);
    }
  };

  const handleDeleteCampaign = async () => {
    if (campaign.status !== 'draft') {
      alert('Only draft campaigns can be deleted');
      return;
    }

    setDeleting(true);
    try {
      const token = localStorage.getItem('BRAND_TOKEN');
      const response = await fetch(`${config.BACKEND_URL}/api/brand/campaigns/${campaign.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (data.status === 'success') {
        alert('Campaign deleted successfully');
        navigate('/brand/campaigns');
      } else {
        alert(data.message || 'Failed to delete campaign');
      }
    } catch (error) {
      console.error('Error deleting campaign:', error);
      alert('Failed to delete campaign');
    } finally {
      setDeleting(false);
      setShowDeleteModal(false);
    }
  };

  const handleViewContent = (creator) => {
    setSelectedContent(creator);
    setContentStatus(creator.contentStatus || 'Pending');
    setShowContentModal(true);
  };

  const handleExtensionAction = async (creatorId, action) => {
    setUpdating(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setCreators(creators.map(creator =>
        creator.id === creatorId
          ? {
              ...creator,
              extensionStatus: action === 'approve' ? creator.extensionStatus.replace('Pending', 'Approved') : 'Rejected'
            }
          : creator
      ));

      alert(`Extension ${action === 'approve' ? 'approved' : 'rejected'} successfully`);
    } catch (error) {
      console.error('Error handling extension:', error);
      alert('Failed to update extension status');
    } finally {
      setUpdating(false);
    }
  };

  const handleParticipationAction = async (creatorId, action) => {
    setUpdating(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setCreators(creators.map(creator =>
        creator.id === creatorId
          ? {
              ...creator,
              participationStatus: action === 'approve' ? 'Approved' : 'Rejected',
              approvedAt: action === 'approve' ? new Date().toISOString().split('T')[0] : null
            }
          : creator
      ));

      alert(`Participation ${action === 'approve' ? 'approved' : 'rejected'} successfully`);
    } catch (error) {
      console.error('Error handling participation:', error);
      alert('Failed to update participation status');
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#121212] text-gray-100 p-6 flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-lime-500"></div>
      </div>
    );
  }

  if (!campaign) {
    return (
      <div className="min-h-screen bg-[#121212] text-gray-100 p-6">
        <div className="text-center py-20">
          <p className="text-xl text-gray-400 FontNoto">Campaign not found</p>
          <button
            onClick={() => navigate('/brand/campaigns')}
            className="mt-4 bg-lime-500 hover:bg-lime-600 text-black px-6 py-2 rounded-lg FontNoto font-medium"
          >
            Back to Campaigns
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#121212] text-gray-100 p-6">
      <Helmet>
        <title>{campaign.title} | Campaign Details</title>
        <meta name="robots" content="noindex, nofollow" />
      </Helmet>

      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/brand/campaigns')}
            className="flex items-center gap-2 text-gray-400 hover:text-lime-400 mb-4 FontNoto transition-colors duration-200"
          >
            <FaArrowLeft />
            Back to Campaigns
          </button>
          
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
            <div className="flex justify-between items-start mb-4">
              <h1 className="text-3xl font-bold text-[#E0FFFA] FontNoto">
                {campaign.title}
              </h1>

              {/* Delete Button for Draft Campaigns */}
              {campaign.status === 'draft' && (
                <button
                  onClick={() => setShowDeleteModal(true)}
                  disabled={deleting}
                  className="bg-red-500/20 hover:bg-red-500/30 text-red-400 px-4 py-2 rounded-lg FontNoto flex items-center gap-2 transition-colors duration-200 disabled:opacity-50"
                  title="Delete draft campaign"
                >
                  <FaTimes className="text-sm" />
                  🗑 Delete
                </button>
              )}
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-400 FontNoto">Status:</span>
                <span className="text-lime-400 font-semibold ml-2 FontNoto capitalize">
                  {campaign.status}
                </span>
              </div>
              <div>
                <span className="text-gray-400 FontNoto">Created:</span>
                <span className="text-gray-300 ml-2 FontNoto">{campaign.createdAt}</span>
              </div>
              <div>
                <span className="text-gray-400 FontNoto">Deadline:</span>
                <span className="text-gray-300 ml-2 FontNoto">{campaign.deadline}</span>
              </div>
              <div>
                <span className="text-gray-400 FontNoto">Budget:</span>
                <span className="text-gray-300 ml-2 FontNoto">${campaign.budget}</span>
              </div>
            </div>
            
            <div className="mt-4">
              <p className="text-gray-300 FontNoto">{campaign.description}</p>
            </div>
          </div>
        </div>

        {/* Creators Table */}
        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden">
          <div className="p-6 border-b border-white/10">
            <h2 className="text-xl font-bold text-[#E0FFFA] FontNoto">
              Creator Applications ({creators.length})
            </h2>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white/5">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Name & Social ID
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Platform
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Performance Score
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Tracking
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Delivery Status
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Content Status
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Extension Status
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Content
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Participation
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-white/10">
                {creators.map((creator) => (
                  <tr key={creator.id} className="hover:bg-white/5 transition-colors duration-200">
                    {/* Name & Social ID */}
                    <td className="px-6 py-4">
                      <div className="flex flex-col">
                        <button
                          onClick={() => handleSocialClick(creator.profileUrl)}
                          className="text-[#E0FFFA] hover:text-lime-400 font-medium FontNoto text-left flex items-center gap-2 transition-colors duration-200"
                        >
                          {creator.name}
                          <FaExternalLinkAlt className="text-xs" />
                        </button>
                        <button
                          onClick={() => handleSocialClick(creator.profileUrl)}
                          className="text-gray-400 hover:text-lime-400 text-sm FontNoto text-left transition-colors duration-200"
                        >
                          {creator.socialId}
                        </button>
                      </div>
                    </td>

                    {/* Platform */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{getPlatformIcon(creator.platform)}</span>
                        <span className="text-gray-300 FontNoto">{creator.platform}</span>
                      </div>
                    </td>

                    {/* Performance Score */}
                    <td className="px-6 py-4">
                      {performanceScores[creator.id] ? (
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{performanceScores[creator.id].colorIndicator}</span>
                          <span
                            className="font-semibold FontNoto text-gray-300 cursor-help"
                            title={performanceScores[creator.id].tooltip}
                          >
                            {performanceScores[creator.id].display}
                          </span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <span className="text-lg">⚪</span>
                          <span
                            className="font-semibold FontNoto text-gray-500 cursor-help"
                            title="Performance Score is calculated after participating in 2 or more campaigns."
                          >
                            —
                          </span>
                        </div>
                      )}
                    </td>

                    {/* Tracking */}
                    <td className="px-6 py-4">
                      <div className="flex flex-col gap-1">
                        <div className="flex items-center gap-2">
                          {creator.tracking ? (
                            <button
                              onClick={() => handleTrackingClick(creator.tracking)}
                              className="text-lime-400 hover:text-lime-300 FontNoto text-sm underline flex items-center gap-1"
                            >
                              {creator.tracking}
                              <FaExternalLinkAlt className="text-xs" />
                            </button>
                          ) : (
                            <span className="text-gray-500 FontNoto text-sm">No tracking</span>
                          )}
                          <button
                            onClick={() => handleTrackingModalOpen(creator)}
                            className="text-gray-400 hover:text-lime-400 p-1"
                            title="Edit tracking"
                          >
                            <FaEdit className="text-xs" />
                          </button>
                        </div>
                        {/* Shipping Info - only show if approved */}
                        {creator.participationStatus === 'Approved' && creator.shippingInfo && (
                          <div className="text-xs text-gray-400 FontNoto mt-1 p-2 bg-white/5 rounded">
                            <div><strong>Name:</strong> {creator.shippingInfo.name}</div>
                            <div><strong>Phone:</strong> {creator.shippingInfo.phone}</div>
                            <div><strong>Address:</strong> {creator.shippingInfo.address}</div>
                          </div>
                        )}
                      </div>
                    </td>

                    {/* Delivery Status */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{getDeliveryStatusIcon(creator.deliveryStatus)}</span>
                        <div className="flex flex-col">
                          <span className={`font-medium FontNoto text-sm ${getDeliveryStatusColor(creator.deliveryStatus)}`}>
                            {creator.deliveryStatus}
                          </span>
                          {creator.deliveredAt && (
                            <span className="text-xs text-gray-400 FontNoto">
                              Delivered on {creator.deliveredAt}
                            </span>
                          )}
                        </div>
                      </div>
                    </td>

                    {/* Content Status */}
                    <td className="px-6 py-4">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium FontNoto ${getStatusColor(creator.contentStatus)}`}>
                        {creator.contentStatus === 'Submitted' ? '✅ Submitted' : '— Not Submitted'}
                      </span>
                    </td>

                    {/* Extension Status */}
                    <td className="px-6 py-4">
                      {creator.extensionStatus ? (
                        <div className="flex flex-col gap-2">
                          <span className={`text-sm FontNoto ${getExtensionStatusColor(creator.extensionStatus)}`}>
                            {creator.extensionStatus}
                          </span>
                          {creator.extensionStatus.includes('Pending') && (
                            <div className="flex gap-1">
                              <button
                                onClick={() => handleExtensionAction(creator.id, 'approve')}
                                disabled={updating}
                                className="bg-green-500/20 hover:bg-green-500/30 text-green-400 px-2 py-1 rounded text-xs FontNoto transition-colors duration-200 disabled:opacity-50"
                              >
                                Approve
                              </button>
                              <button
                                onClick={() => handleExtensionAction(creator.id, 'reject')}
                                disabled={updating}
                                className="bg-red-500/20 hover:bg-red-500/30 text-red-400 px-2 py-1 rounded text-xs FontNoto transition-colors duration-200 disabled:opacity-50"
                              >
                                Reject
                              </button>
                            </div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-500 text-sm FontNoto">—</span>
                      )}
                    </td>

                    {/* Content */}
                    <td className="px-6 py-4">
                      {creator.hasContent ? (
                        <button
                          onClick={() => handleViewContent(creator)}
                          className="bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 px-3 py-1 rounded text-sm FontNoto transition-colors duration-200"
                        >
                          🔗 View Content
                        </button>
                      ) : (
                        <span className="text-gray-500 text-sm FontNoto">No content</span>
                      )}
                    </td>

                    {/* Participation */}
                    <td className="px-6 py-4">
                      {creator.participationStatus === 'Applied' ? (
                        <div className="flex gap-1">
                          <button
                            onClick={() => handleParticipationAction(creator.id, 'approve')}
                            disabled={updating}
                            className="bg-green-500/20 hover:bg-green-500/30 text-green-400 px-2 py-1 rounded text-xs FontNoto flex items-center gap-1 transition-colors duration-200 disabled:opacity-50"
                          >
                            <FaCheck className="text-xs" />
                            Approve
                          </button>
                          <button
                            onClick={() => handleParticipationAction(creator.id, 'reject')}
                            disabled={updating}
                            className="bg-red-500/20 hover:bg-red-500/30 text-red-400 px-2 py-1 rounded text-xs FontNoto flex items-center gap-1 transition-colors duration-200 disabled:opacity-50"
                          >
                            <FaBan className="text-xs" />
                            Reject
                          </button>
                        </div>
                      ) : (
                        <span className={`px-3 py-1 rounded-full text-sm font-medium FontNoto ${getParticipationStatusColor(creator.participationStatus)}`}>
                          {creator.participationStatus === 'Approved' ? '✅ Approved' :
                           creator.participationStatus === 'Rejected' ? '❌ Rejected' : creator.participationStatus}
                        </span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {creators.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-400 FontNoto">No creator applications yet</p>
            </div>
          )}
        </div>

        {/* Tracking Modal */}
        {showTrackingModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-[#1f1f1f] rounded-2xl p-6 w-full max-w-md border border-white/10">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold text-[#E0FFFA] FontNoto">
                  Update Tracking - {selectedCreator?.name}
                </h3>
                <button
                  onClick={handleTrackingModalClose}
                  className="text-gray-400 hover:text-white"
                >
                  <FaTimes />
                </button>
              </div>

              <div className="space-y-4">
                {/* Courier Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2 FontNoto">
                    Courier
                  </label>
                  <select
                    value={trackingForm.courier}
                    onChange={(e) => setTrackingForm({...trackingForm, courier: e.target.value})}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white FontNoto focus:outline-none focus:ring-2 focus:ring-lime-500"
                  >
                    <option value="UPS">UPS</option>
                    <option value="FedEx">FedEx</option>
                    <option value="USPS">USPS</option>
                    <option value="Other">Other</option>
                  </select>
                </div>

                {/* Custom Courier Input */}
                {trackingForm.courier === 'Other' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2 FontNoto">
                      Custom Courier Name
                    </label>
                    <input
                      type="text"
                      value={trackingForm.customCourier}
                      onChange={(e) => setTrackingForm({...trackingForm, customCourier: e.target.value})}
                      placeholder="Enter courier name"
                      className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 FontNoto focus:outline-none focus:ring-2 focus:ring-lime-500"
                    />
                  </div>
                )}

                {/* Tracking Number */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2 FontNoto">
                    Tracking Number (Max 50 characters)
                  </label>
                  <input
                    type="text"
                    value={trackingForm.trackingNumber}
                    onChange={(e) => setTrackingForm({...trackingForm, trackingNumber: e.target.value})}
                    placeholder="Enter tracking number"
                    maxLength={50}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 FontNoto focus:outline-none focus:ring-2 focus:ring-lime-500"
                  />
                  <p className="text-xs text-gray-400 mt-1 FontNoto">
                    Creator will receive: https://www.17track.net/en/track?nums={trackingForm.trackingNumber}
                  </p>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 pt-4">
                  <button
                    onClick={handleTrackingSubmit}
                    disabled={updating || !trackingForm.trackingNumber.trim()}
                    className="flex-1 bg-lime-500 hover:bg-lime-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg FontNoto font-medium transition-colors duration-200"
                  >
                    {updating ? 'Saving...' : 'Save & Send Email'}
                  </button>
                  <button
                    onClick={handleTrackingModalClose}
                    disabled={updating}
                    className="px-4 py-2 bg-white/10 hover:bg-white/20 text-gray-300 rounded-lg FontNoto transition-colors duration-200"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {showDeleteModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-[#1f1f1f] rounded-2xl p-6 w-full max-w-md border border-white/10">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold text-red-400 FontNoto">
                  Delete Campaign
                </h3>
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="text-gray-400 hover:text-white"
                  disabled={deleting}
                >
                  <FaTimes />
                </button>
              </div>

              <div className="mb-6">
                <p className="text-gray-300 FontNoto mb-4">
                  Are you sure you want to delete the campaign "{campaign.title}"?
                </p>
                <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                  <p className="text-red-400 text-sm FontNoto">
                    ⚠️ This action cannot be undone. All campaign data will be permanently deleted.
                  </p>
                </div>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={handleDeleteCampaign}
                  disabled={deleting}
                  className="flex-1 bg-red-500 hover:bg-red-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg FontNoto font-medium transition-colors duration-200"
                >
                  {deleting ? 'Deleting...' : 'Delete Campaign'}
                </button>
                <button
                  onClick={() => setShowDeleteModal(false)}
                  disabled={deleting}
                  className="px-4 py-2 bg-white/10 hover:bg-white/20 text-gray-300 rounded-lg FontNoto transition-colors duration-200"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Content View Modal */}
        {showContentModal && selectedContent && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-[#1f1f1f] rounded-2xl p-6 w-full max-w-2xl border border-white/10 max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold text-[#E0FFFA] FontNoto">
                  Content Submission - {selectedContent.name}
                </h3>
                <button
                  onClick={() => setShowContentModal(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <FaTimes />
                </button>
              </div>

              <div className="space-y-6">
                {/* User Info */}
                <div className="bg-white/5 rounded-lg p-4">
                  <h4 className="text-lg font-semibold text-[#E0FFFA] mb-3 FontNoto">User Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-400 FontNoto">Name:</span>
                      <span className="text-gray-300 ml-2 FontNoto">{selectedContent.name}</span>
                    </div>
                    <div>
                      <span className="text-gray-400 FontNoto">Email:</span>
                      <span className="text-gray-300 ml-2 FontNoto">{selectedContent.email || 'N/A'}</span>
                    </div>
                    <div>
                      <span className="text-gray-400 FontNoto">TikTok ID:</span>
                      <button
                        onClick={() => handleSocialClick(selectedContent.profileUrl)}
                        className="text-lime-400 hover:text-lime-300 ml-2 FontNoto underline"
                      >
                        {selectedContent.socialId}
                      </button>
                    </div>
                    <div>
                      <span className="text-gray-400 FontNoto">Instagram ID:</span>
                      <button
                        onClick={() => handleSocialClick(selectedContent.profileUrl)}
                        className="text-lime-400 hover:text-lime-300 ml-2 FontNoto underline"
                      >
                        {selectedContent.socialId}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Content URLs */}
                <div className="bg-white/5 rounded-lg p-4">
                  <h4 className="text-lg font-semibold text-[#E0FFFA] mb-3 FontNoto">Content URLs</h4>
                  <div className="space-y-2">
                    {selectedContent.contentUrls?.instagram && (
                      <div>
                        <span className="text-gray-400 FontNoto">Instagram Post:</span>
                        <button
                          onClick={() => window.open(selectedContent.contentUrls.instagram, '_blank')}
                          className="text-lime-400 hover:text-lime-300 ml-2 FontNoto underline"
                        >
                          {selectedContent.contentUrls.instagram}
                        </button>
                      </div>
                    )}
                    {selectedContent.contentUrls?.tiktok && (
                      <div>
                        <span className="text-gray-400 FontNoto">TikTok Video:</span>
                        <button
                          onClick={() => window.open(selectedContent.contentUrls.tiktok, '_blank')}
                          className="text-lime-400 hover:text-lime-300 ml-2 FontNoto underline"
                        >
                          {selectedContent.contentUrls.tiktok}
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Content Status */}
                <div className="bg-white/5 rounded-lg p-4">
                  <h4 className="text-lg font-semibold text-[#E0FFFA] mb-3 FontNoto">Content Status</h4>
                  <select
                    value={contentStatus}
                    onChange={(e) => setContentStatus(e.target.value)}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white FontNoto focus:outline-none focus:ring-2 focus:ring-lime-500"
                  >
                    <option value="Pending">Pending</option>
                    <option value="Approved">Approved</option>
                    <option value="Rejected">Rejected</option>
                  </select>
                </div>

                {/* Submitted At */}
                <div className="bg-white/5 rounded-lg p-4">
                  <h4 className="text-lg font-semibold text-[#E0FFFA] mb-3 FontNoto">Submission Details</h4>
                  <div className="text-sm">
                    <span className="text-gray-400 FontNoto">Submitted At:</span>
                    <span className="text-gray-300 ml-2 FontNoto">June 2, 2025, 12:52 PM</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 pt-4">
                  <button
                    onClick={() => {
                      // Update content status
                      setCreators(creators.map(creator =>
                        creator.id === selectedContent.id
                          ? { ...creator, contentStatus: contentStatus }
                          : creator
                      ));
                      setShowContentModal(false);
                      alert('Content status updated successfully');
                    }}
                    className="flex-1 bg-lime-500 hover:bg-lime-600 text-white px-4 py-2 rounded-lg FontNoto font-medium transition-colors duration-200"
                  >
                    Save & Update Status
                  </button>
                  <button
                    onClick={() => setShowContentModal(false)}
                    className="px-4 py-2 bg-white/10 hover:bg-white/20 text-gray-300 rounded-lg FontNoto transition-colors duration-200"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
