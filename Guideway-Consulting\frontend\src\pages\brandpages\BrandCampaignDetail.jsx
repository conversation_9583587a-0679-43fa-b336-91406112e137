import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { FaArrowLeft, FaExternalLinkAlt, FaEdit, FaSave, FaTimes } from 'react-icons/fa';
import config from '../../config';

export default function BrandCampaignDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [campaign, setCampaign] = useState(null);
  const [creators, setCreators] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editingTracking, setEditingTracking] = useState(null);
  const [trackingInput, setTrackingInput] = useState('');

  // Mock campaign data
  const mockCampaign = {
    id: parseInt(id),
    title: 'Glow Serum Ampoule',
    status: 'active',
    createdAt: '2025-05-18',
    deadline: '2025-06-03',
    description: 'Promote our new vitamin C glow serum to skincare enthusiasts',
    budget: 5000,
    targetAudience: 'Skincare enthusiasts, ages 18-35',
    requirements: 'Must have 10K+ followers, skincare niche'
  };

  // Mock creators data
  const mockCreators = [
    {
      id: 1,
      name: 'Sarah Kim',
      socialId: '@sarahskincare',
      platform: 'Instagram',
      profileUrl: 'https://instagram.com/sarahskincare',
      performanceScore: 8.5,
      tracking: 'TW123456789KR',
      contentStatus: 'Approved',
      appliedAt: '2025-05-20',
      approvedAt: '2025-05-22'
    },
    {
      id: 2,
      name: 'Jessica Lee',
      socialId: '@jessbeauty',
      platform: 'TikTok',
      profileUrl: 'https://tiktok.com/@jessbeauty',
      performanceScore: 9.2,
      tracking: '',
      contentStatus: 'Applied',
      appliedAt: '2025-05-21',
      approvedAt: null
    },
    {
      id: 3,
      name: 'Emma Chen',
      socialId: '@emmaskintips',
      platform: 'Instagram',
      profileUrl: 'https://instagram.com/emmaskintips',
      performanceScore: 7.8,
      tracking: 'TW987654321KR',
      contentStatus: 'Not Submitted',
      appliedAt: '2025-05-19',
      approvedAt: '2025-05-21'
    },
    {
      id: 4,
      name: 'Mia Park',
      socialId: '@miabeautylife',
      platform: 'TikTok',
      profileUrl: 'https://tiktok.com/@miabeautylife',
      performanceScore: 8.9,
      tracking: 'TW456789123KR',
      contentStatus: 'Submitted',
      appliedAt: '2025-05-18',
      approvedAt: '2025-05-20'
    }
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setCampaign(mockCampaign);
      setCreators(mockCreators);
      setLoading(false);
    }, 1000);
  }, [id]);

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'applied': return 'text-yellow-400 bg-yellow-400/10';
      case 'approved': return 'text-green-400 bg-green-400/10';
      case 'not submitted': return 'text-red-400 bg-red-400/10';
      case 'submitted': return 'text-blue-400 bg-blue-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  const getPlatformIcon = (platform) => {
    return platform === 'Instagram' ? '📷' : '🎵';
  };

  const getPerformanceColor = (score) => {
    if (score >= 9) return 'text-green-400';
    if (score >= 7) return 'text-yellow-400';
    return 'text-red-400';
  };

  const handleSocialClick = (url) => {
    window.open(url, '_blank');
  };

  const handleTrackingEdit = (creatorId, currentTracking) => {
    setEditingTracking(creatorId);
    setTrackingInput(currentTracking);
  };

  const handleTrackingSave = (creatorId) => {
    setCreators(creators.map(creator => 
      creator.id === creatorId 
        ? { ...creator, tracking: trackingInput }
        : creator
    ));
    setEditingTracking(null);
    setTrackingInput('');
  };

  const handleTrackingCancel = () => {
    setEditingTracking(null);
    setTrackingInput('');
  };

  const handleTrackingClick = (tracking) => {
    if (tracking) {
      window.open(`https://www.17track.net/en/track#nums=${tracking}`, '_blank');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#121212] text-gray-100 p-6 flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-lime-500"></div>
      </div>
    );
  }

  if (!campaign) {
    return (
      <div className="min-h-screen bg-[#121212] text-gray-100 p-6">
        <div className="text-center py-20">
          <p className="text-xl text-gray-400 FontNoto">Campaign not found</p>
          <button
            onClick={() => navigate('/brand/campaigns')}
            className="mt-4 bg-lime-500 hover:bg-lime-600 text-black px-6 py-2 rounded-lg FontNoto font-medium"
          >
            Back to Campaigns
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#121212] text-gray-100 p-6">
      <Helmet>
        <title>{campaign.title} | Campaign Details</title>
        <meta name="robots" content="noindex, nofollow" />
      </Helmet>

      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/brand/campaigns')}
            className="flex items-center gap-2 text-gray-400 hover:text-lime-400 mb-4 FontNoto transition-colors duration-200"
          >
            <FaArrowLeft />
            Back to Campaigns
          </button>
          
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
            <h1 className="text-3xl font-bold text-[#E0FFFA] mb-4 FontNoto">
              {campaign.title}
            </h1>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-400 FontNoto">Status:</span>
                <span className="text-lime-400 font-semibold ml-2 FontNoto capitalize">
                  {campaign.status}
                </span>
              </div>
              <div>
                <span className="text-gray-400 FontNoto">Created:</span>
                <span className="text-gray-300 ml-2 FontNoto">{campaign.createdAt}</span>
              </div>
              <div>
                <span className="text-gray-400 FontNoto">Deadline:</span>
                <span className="text-gray-300 ml-2 FontNoto">{campaign.deadline}</span>
              </div>
              <div>
                <span className="text-gray-400 FontNoto">Budget:</span>
                <span className="text-gray-300 ml-2 FontNoto">${campaign.budget}</span>
              </div>
            </div>
            
            <div className="mt-4">
              <p className="text-gray-300 FontNoto">{campaign.description}</p>
            </div>
          </div>
        </div>

        {/* Creators Table */}
        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden">
          <div className="p-6 border-b border-white/10">
            <h2 className="text-xl font-bold text-[#E0FFFA] FontNoto">
              Creator Applications ({creators.length})
            </h2>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white/5">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Name & Social ID
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Platform
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Performance Score
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Tracking
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300 FontNoto">
                    Content Status
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-white/10">
                {creators.map((creator) => (
                  <tr key={creator.id} className="hover:bg-white/5 transition-colors duration-200">
                    {/* Name & Social ID */}
                    <td className="px-6 py-4">
                      <div className="flex flex-col">
                        <button
                          onClick={() => handleSocialClick(creator.profileUrl)}
                          className="text-[#E0FFFA] hover:text-lime-400 font-medium FontNoto text-left flex items-center gap-2 transition-colors duration-200"
                        >
                          {creator.name}
                          <FaExternalLinkAlt className="text-xs" />
                        </button>
                        <button
                          onClick={() => handleSocialClick(creator.profileUrl)}
                          className="text-gray-400 hover:text-lime-400 text-sm FontNoto text-left transition-colors duration-200"
                        >
                          {creator.socialId}
                        </button>
                      </div>
                    </td>

                    {/* Platform */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{getPlatformIcon(creator.platform)}</span>
                        <span className="text-gray-300 FontNoto">{creator.platform}</span>
                      </div>
                    </td>

                    {/* Performance Score */}
                    <td className="px-6 py-4">
                      <span className={`font-semibold FontNoto ${getPerformanceColor(creator.performanceScore)}`}>
                        {creator.performanceScore}/10
                      </span>
                    </td>

                    {/* Tracking */}
                    <td className="px-6 py-4">
                      {editingTracking === creator.id ? (
                        <div className="flex items-center gap-2">
                          <input
                            type="text"
                            value={trackingInput}
                            onChange={(e) => setTrackingInput(e.target.value)}
                            className="bg-white/10 border border-white/20 rounded px-2 py-1 text-sm text-white FontNoto focus:outline-none focus:ring-2 focus:ring-lime-500"
                            placeholder="Enter tracking number"
                          />
                          <button
                            onClick={() => handleTrackingSave(creator.id)}
                            className="text-green-400 hover:text-green-300 p-1"
                          >
                            <FaSave />
                          </button>
                          <button
                            onClick={handleTrackingCancel}
                            className="text-red-400 hover:text-red-300 p-1"
                          >
                            <FaTimes />
                          </button>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          {creator.tracking ? (
                            <button
                              onClick={() => handleTrackingClick(creator.tracking)}
                              className="text-lime-400 hover:text-lime-300 FontNoto text-sm underline flex items-center gap-1"
                            >
                              {creator.tracking}
                              <FaExternalLinkAlt className="text-xs" />
                            </button>
                          ) : (
                            <span className="text-gray-500 FontNoto text-sm">No tracking</span>
                          )}
                          <button
                            onClick={() => handleTrackingEdit(creator.id, creator.tracking)}
                            className="text-gray-400 hover:text-lime-400 p-1"
                          >
                            <FaEdit className="text-xs" />
                          </button>
                        </div>
                      )}
                    </td>

                    {/* Content Status */}
                    <td className="px-6 py-4">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium FontNoto ${getStatusColor(creator.contentStatus)}`}>
                        {creator.contentStatus}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {creators.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-400 FontNoto">No creator applications yet</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
