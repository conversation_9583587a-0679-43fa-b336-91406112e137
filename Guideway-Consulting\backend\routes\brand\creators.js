const express = require("express");
const { VerifyToken } = require("../../middlewares/auth");
const { sendTrackingEmail, sendApprovalEmail, sendRejectionEmail } = require("../../functions/sendEmail");
const PerformanceService = require("../../services/performanceService");

const brandCreatorRouter = express.Router();

// Mock creator applications data (replace with actual database queries)
const mockCreatorApplications = [
  {
    id: 1,
    campaignId: 1,
    creatorId: 'creator1',
    name: '<PERSON>',
    email: '<EMAIL>',
    socialId: '@sarahskincare',
    platform: 'Instagram',
    profileUrl: 'https://instagram.com/sarahskincare',
    performanceScore: 8.5,
    tracking: 'TW123456789KR',
    courier: 'UPS',
    contentStatus: 'Approved',
    appliedAt: '2025-05-20',
    approvedAt: '2025-05-22'
  },
  {
    id: 2,
    campaignId: 1,
    creatorId: 'creator2',
    name: '<PERSON>',
    email: '<EMAIL>',
    socialId: '@jessbeauty',
    platform: 'TikTok',
    profileUrl: 'https://tiktok.com/@jessbeauty',
    performanceScore: 9.2,
    tracking: '',
    courier: '',
    contentStatus: 'Applied',
    appliedAt: '2025-05-21',
    approvedAt: null
  }
];

// GET /api/brand/creators/:campaignId - Get creators for a specific campaign
brandCreatorRouter.get("/:campaignId", VerifyToken, async (req, res) => {
  try {
    const { campaignId } = req.params;
    
    // Filter creators by campaign ID
    const creators = mockCreatorApplications.filter(
      creator => creator.campaignId === parseInt(campaignId)
    );

    res.json({
      status: "success",
      data: creators
    });
  } catch (error) {
    console.error("Error fetching creators:", error);
    res.json({
      status: "failed",
      message: "Failed to fetch creators"
    });
  }
});

// PUT /api/brand/creators/:creatorId/tracking - Update tracking information
brandCreatorRouter.put("/:creatorId/tracking", VerifyToken, async (req, res) => {
  try {
    const { creatorId } = req.params;
    const { trackingNumber, courier } = req.body;

    // Validate input
    if (!trackingNumber || trackingNumber.length > 50) {
      return res.json({
        status: "failed",
        message: "Tracking number is required and must be less than 50 characters"
      });
    }

    // Find the creator application
    const creatorIndex = mockCreatorApplications.findIndex(
      creator => creator.id === parseInt(creatorId)
    );

    if (creatorIndex === -1) {
      return res.json({
        status: "failed",
        message: "Creator not found"
      });
    }

    const creator = mockCreatorApplications[creatorIndex];
    
    // Update tracking information
    mockCreatorApplications[creatorIndex] = {
      ...creator,
      tracking: trackingNumber,
      courier: courier || 'Other',
      trackingUpdatedAt: new Date().toISOString()
    };

    // Send tracking email to creator
    try {
      await sendTrackingEmail(
        creator.email,
        creator.name,
        `Campaign #${creator.campaignId}`, // Replace with actual campaign title
        trackingNumber
      );
    } catch (emailError) {
      console.error("Failed to send tracking email:", emailError);
      // Don't fail the request if email fails
    }

    // Update performance score for tracking input
    try {
      await PerformanceService.onTrackingInput(creator.creatorId, creator.campaignId);
    } catch (performanceError) {
      console.error("Failed to update performance score:", performanceError);
      // Don't fail the request if performance update fails
    }

    res.json({
      status: "success",
      message: "Tracking information updated and email sent",
      data: mockCreatorApplications[creatorIndex]
    });
  } catch (error) {
    console.error("Error updating tracking:", error);
    res.json({
      status: "failed",
      message: "Failed to update tracking information"
    });
  }
});

// PUT /api/brand/creators/:creatorId/status - Update creator application status
brandCreatorRouter.put("/:creatorId/status", VerifyToken, async (req, res) => {
  try {
    const { creatorId } = req.params;
    const { status } = req.body;

    // Validate status
    const validStatuses = ['Applied', 'Approved', 'Rejected', 'Not Submitted', 'Submitted'];
    if (!validStatuses.includes(status)) {
      return res.json({
        status: "failed",
        message: "Invalid status. Must be one of: " + validStatuses.join(', ')
      });
    }

    // Find the creator application
    const creatorIndex = mockCreatorApplications.findIndex(
      creator => creator.id === parseInt(creatorId)
    );

    if (creatorIndex === -1) {
      return res.json({
        status: "failed",
        message: "Creator not found"
      });
    }

    const creator = mockCreatorApplications[creatorIndex];
    const previousStatus = creator.contentStatus;
    
    // Update status
    mockCreatorApplications[creatorIndex] = {
      ...creator,
      contentStatus: status,
      statusUpdatedAt: new Date().toISOString(),
      ...(status === 'Approved' && { approvedAt: new Date().toISOString() }),
      ...(status === 'Rejected' && { rejectedAt: new Date().toISOString() })
    };

    // Send appropriate email based on status change
    try {
      if (status === 'Approved' && previousStatus !== 'Approved') {
        await sendApprovalEmail(
          creator.email,
          creator.name,
          `Campaign #${creator.campaignId}` // Replace with actual campaign title
        );
      } else if (status === 'Rejected' && previousStatus !== 'Rejected') {
        await sendRejectionEmail(
          creator.email,
          creator.name,
          `Campaign #${creator.campaignId}` // Replace with actual campaign title
        );
      }
    } catch (emailError) {
      console.error("Failed to send status email:", emailError);
      // Don't fail the request if email fails
    }

    // Update performance score for status change
    try {
      await PerformanceService.onStatusChange(creator.creatorId, creator.campaignId, status);
    } catch (performanceError) {
      console.error("Failed to update performance score:", performanceError);
      // Don't fail the request if performance update fails
    }

    res.json({
      status: "success",
      message: `Creator status updated to ${status}${status === 'Approved' || status === 'Rejected' ? ' and email sent' : ''}`,
      data: mockCreatorApplications[creatorIndex]
    });
  } catch (error) {
    console.error("Error updating creator status:", error);
    res.json({
      status: "failed",
      message: "Failed to update creator status"
    });
  }
});

module.exports = brandCreatorRouter;
